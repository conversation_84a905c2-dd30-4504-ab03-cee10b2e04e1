export default defineEventHandler(async (event) => {
  try {
    // 测试写入数据到文件系统存储
    await useStorage('fSystem').setItem('test-key', {
      message: 'Hello File System!',
      timestamp: new Date().toISOString(),
      data: { count: 42, active: true }
    })

    // 测试读取数据
    const data = await useStorage('fSystem').getItem('test-key')

    // 获取所有键
    const keys = await useStorage('fSystem').getKeys()
    
    return {
      success: true,
      message: '文件系统存储测试成功',
      writtenData: {
        message: 'Hello File System!',
        timestamp: new Date().toISOString(),
        data: { count: 42, active: true }
      },
      readData: data,
      allKeys: keys,
      storageInfo: {
        hasFileSystemStorage: !!useStorage('fSystem'),
        storageType: typeof useStorage('fSystem')
      }
    }
  } catch (error) {
    console.error('存储测试失败:', error)
    
    return {
      success: false,
      error: error.message,
      errorType: error.constructor.name,
      stack: error.stack
    }
  }
})
