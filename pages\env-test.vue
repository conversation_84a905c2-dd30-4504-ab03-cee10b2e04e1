<script setup lang="ts">
// 获取运行时配置
const config = useRuntimeConfig()

// 服务器端和客户端都可以访问 public 配置
const publicToken = config.public.apiTokenForText

// 只有服务器端可以访问私有配置
const privateToken = import.meta.server ? config.apiTokenForText : '客户端无法访问私有配置'

console.log('Public Token:', publicToken)
console.log('Private Token:', privateToken)
</script>

<template>
  <div class="env-test">
    <h1>环境变量测试</h1>
    
    <div class="config-section">
      <h2>🌐 公开配置 (public)</h2>
      <p><strong>客户端和服务端都可以访问</strong></p>
      <div class="config-item">
        <label>apiTokenForText:</label>
        <code>{{ publicToken || '未设置' }}</code>
      </div>
    </div>
    
    <div class="config-section">
      <h2>🔒 私有配置 (server-only)</h2>
      <p><strong>只有服务端可以访问</strong></p>
      <div class="config-item">
        <label>apiTokenForText:</label>
        <code>{{ privateToken || '未设置' }}</code>
      </div>
    </div>
    
    <div class="info-section">
      <h3>📝 环境变量命名规则</h3>
      <ul>
        <li><code>API_TOKEN_FOR_TEXT</code> → 服务器端私有</li>
        <li><code>NUXT_PUBLIC_API_TOKEN_FOR_TEXT</code> → 客户端公开</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.env-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.config-section {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.config-item {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.config-item label {
  font-weight: bold;
  margin-right: 10px;
  min-width: 150px;
}

.config-item code {
  background-color: #e7e7e7;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.info-section {
  margin-top: 30px;
  padding: 15px;
  background-color: #e3f2fd;
  border-radius: 8px;
}

.info-section ul {
  margin: 10px 0;
  padding-left: 20px;
}

.info-section code {
  background-color: #fff;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}
</style>
