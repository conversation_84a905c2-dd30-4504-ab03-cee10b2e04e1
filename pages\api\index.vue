<script setup>
definePageMeta({
  layout:'empty'
})


// 将用户的 headers 和 cookies 转发到 `/api/cookies`
const headers = useRequestEvent(['cookie'])
console.log('[33m [ headers ]-4 [0m', headers)
const { data } = await useFetch('/api/cookies', {
	headers,
})

const nuxtApp = useNuxtApp()

const config  = useRuntimeConfig()

console.log('%c [  ]-18', 'font-size:13px; background:#2737d2; color:#6b7bff;', config);

if(import.meta.client ){

  const test =  await  useFetch('/proxy/test')
  console.log('%c [ test ]-23', 'font-size:13px; background:#cad498; color:#ffffdc;', test);
}



</script>

<template>
	<div>api</div>
  <p>{{ nuxtApp.payload.data }}</p>

  <p>{{ data }}</p>

  {{ config }}

  <br>
  {{ test }}

<img src="/image7.png" alt="">
</template>
