<script setup lang="ts">
import '~/assets/css/first.css'
import { getNow } from '@/test/get-now'
import { useUser } from '@/composables/use-user';


const name = ref('<PERSON>')
// useHead({
// 	title: "this's abouts",
// 	titleTemplate: (titleChunk) => {
// 		console.log('[30m [ titleChunk ]-5 [0m', titleChunk)
// 		return titleChunk ? `${titleChunk} %separator %name ` : "Rex's Nuxt"
// 	},
// 	templateParams: {
// 		name: '站点标题',
// 		separator: '-',
// 	},
// })

const someErrorLogger= (error)=>{

  console.log('%c [ someErrorLogger ]-18', 'font-size:13px; background:#eefea4; color:#ffffe8;',error );
}

console.log('%c [ getNow ]-22', 'font-size:13px; background:#01cf2d; color:#45ff71;', getNow);
getNow();

const {user} = await useUser();


const handleClose = () => {
  ElMessageBox.confirm('Are you sure to close this dialog?')
    .then(() => {
    })
    .catch(() => {
      // catch error
    })
}

</script>

<template>
	<!-- <div>
		<h1>欢迎来到首页</h1>
		<ButtonText> 这是一个自动导入的组件 </ButtonText>
		<nuxt-link to="/abouts"> 跳转到关于页面 </nuxt-link>
	</div> -->
	<!-- 一些内容 -->
<div>
  <AppHead/>

  	<NuxtErrorBoundary @error="someErrorLogger">
		<!-- 使用默认插槽渲染你的内容 -->
		<template #error="{ error, clearError }">
			你可以在这里本地显示错误：{{ error }}
			<button @click="clearError">这将清除错误。</button>


		</template>
	</NuxtErrorBoundary>
  user:
  {{ user }}

  <br>

  name :{{ name }}

  <el-input v-model="name"/>

   <el-button plain @click="handleClose">
    Click to open the Dialog
  </el-button>


  <hr>

  <ButtonText>测试</ButtonText>

  <hr>
  
  <Test v-model="name"/>
  <hr>
  <LazyData hydrate-on-visible />


</div>
</template>

<style lang="less" scoped>
@import url('~/assets/css/second.css');

div {
	h1 {
		background-color: palevioletred;
	}

	.text-btn {
		height: 100px;
		cursor: pointer;
	}
}
</style>
