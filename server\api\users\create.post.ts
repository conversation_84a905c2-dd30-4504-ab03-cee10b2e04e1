// 创建用户的 API 文档定义
defineRouteMeta({
  openAPI: {
    tags: ["用户管理"],
    summary: "创建新用户",
    description: "创建一个新的用户账户",
    requestBody: {
      required: true,
      content: {
        "application/json": {
          schema: {
            type: "object",
            required: ["name", "email"],
            properties: {
              name: {
                type: "string",
                minLength: 2,
                maxLength: 50,
                description: "用户名，2-50个字符"
              },
              email: {
                type: "string",
                format: "email",
                description: "邮箱地址，必须是有效的邮箱格式"
              },
              avatar: {
                type: "string",
                format: "uri",
                description: "头像URL，可选"
              }
            }
          }
        }
      }
    },
    responses: {
      201: {
        description: "用户创建成功",
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                success: { type: "boolean", example: true },
                message: { type: "string", example: "用户创建成功" },
                user: {
                  type: "object",
                  properties: {
                    id: { type: "integer", description: "新创建的用户ID" },
                    name: { type: "string", description: "用户名" },
                    email: { type: "string", description: "邮箱地址" },
                    avatar: { type: "string", description: "头像URL" },
                    createdAt: { type: "string", format: "date-time", description: "创建时间" }
                  }
                }
              }
            }
          }
        }
      },
      400: {
        description: "请求参数错误",
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                success: { type: "boolean", example: false },
                error: { type: "string", description: "错误信息" },
                details: {
                  type: "array",
                  items: { type: "string" },
                  description: "详细的验证错误信息"
                }
              }
            }
          }
        }
      },
      409: {
        description: "邮箱已存在",
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                success: { type: "boolean", example: false },
                error: { type: "string", example: "邮箱已存在" }
              }
            }
          }
        }
      }
    }
  }
});

// 实际的 API 处理函数
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    
    // 参数验证
    const errors: string[] = [];
    
    if (!body.name || typeof body.name !== 'string') {
      errors.push('用户名是必需的');
    } else if (body.name.length < 2 || body.name.length > 50) {
      errors.push('用户名长度必须在2-50个字符之间');
    }
    
    if (!body.email || typeof body.email !== 'string') {
      errors.push('邮箱是必需的');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(body.email)) {
      errors.push('邮箱格式不正确');
    }
    
    if (errors.length > 0) {
      setResponseStatus(event, 400);
      return {
        success: false,
        error: '请求参数错误',
        details: errors
      };
    }
    
    // 模拟检查邮箱是否已存在
    const existingEmails = ['<EMAIL>', '<EMAIL>'];
    if (existingEmails.includes(body.email)) {
      setResponseStatus(event, 409);
      return {
        success: false,
        error: '邮箱已存在'
      };
    }
    
    // 模拟创建用户
    const newUser = {
      id: Math.floor(Math.random() * 1000) + 100,
      name: body.name,
      email: body.email,
      avatar: body.avatar || '/avatars/default.jpg',
      createdAt: new Date().toISOString()
    };
    
    setResponseStatus(event, 201);
    return {
      success: true,
      message: '用户创建成功',
      user: newUser
    };
    
  } catch (error) {
    setResponseStatus(event, 500);
    return {
      success: false,
      error: '服务器内部错误'
    };
  }
});
