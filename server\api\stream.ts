// 移除 useState 导入，因为它不能在服务器端使用

export default defineEventHandler(async (event) => {
 const eventStream = createEventStream(event);
  let count = 0; // 使用普通变量替代 useState

  // Send a message every second
  const interval = setInterval(async () => {
    await eventStream.push("Hello world " + count);
    count++;
    if(count >10){
      clearInterval(interval);
    }
  }, 1000);

  // cleanup the interval and close the stream when the connection is terminated
  eventStream.onClosed(async () => {
    console.log("closing SSE...");
    clearInterval(interval);
    await eventStream.close();
  });

  return eventStream.send();
})