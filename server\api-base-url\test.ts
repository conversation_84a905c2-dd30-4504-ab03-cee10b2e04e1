defineRouteMeta({
  openAPI: {
    tags: ["test"],
    description: "测试路由描述",
    parameters: [{ in: "query", name: "test", required: true }],
  },
});

export default defineEventHandler(async () => {
  const ff = await useStorage('fSystem')

  ff.setItem('11', 333)
  ff.setItem('22', 33)

  const as = await useStorage('assets:templates')
  const i = await as.getItem(`success.html`)
  console.log('%c [ as ]-16', 'font-size:13px; background:#e3ace9; color:#fff0ff;', i);



  return 'hello world'
  // return as.getItem('images/image.png')
});