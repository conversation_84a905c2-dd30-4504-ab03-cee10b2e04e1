<script setup lang="ts">
const testResult = ref(null)
const loading = ref(false)
const error = ref('')

// 测试存储功能
const testStorage = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const { data } = await $fetch('/api/test-storage')
    testResult.value = data
  } catch (err) {
    error.value = err.message || '测试失败'
    console.error('存储测试错误:', err)
  } finally {
    loading.value = false
  }
}

// 页面加载时自动测试
onMounted(() => {
  testStorage()
})
</script>

<template>
  <div class="storage-test">
    <h1>🗄️ Nitro 存储测试</h1>
    
    <div class="test-section">
      <button 
        @click="testStorage" 
        :disabled="loading"
        class="test-button"
      >
        {{ loading ? '测试中...' : '重新测试存储' }}
      </button>
    </div>
    
    <!-- 错误信息 -->
    <div v-if="error" class="error-section">
      <h3>❌ 测试失败</h3>
      <pre>{{ error }}</pre>
    </div>
    
    <!-- 成功结果 -->
    <div v-if="testResult && !error" class="result-section">
      <h3>✅ 测试结果</h3>
      
      <div class="result-item">
        <h4>📝 写入的数据:</h4>
        <pre>{{ JSON.stringify(testResult.writtenData, null, 2) }}</pre>
      </div>
      
      <div class="result-item">
        <h4>📖 读取的数据:</h4>
        <pre>{{ JSON.stringify(testResult.readData, null, 2) }}</pre>
      </div>
      
      <div class="result-item">
        <h4>🔑 所有键:</h4>
        <pre>{{ JSON.stringify(testResult.allKeys, null, 2) }}</pre>
      </div>
      
      <div class="result-item">
        <h4>ℹ️ 存储信息:</h4>
        <pre>{{ JSON.stringify(testResult.storageInfo, null, 2) }}</pre>
      </div>
    </div>
    
    <!-- 配置说明 -->
    <div class="config-section">
      <h3>⚙️ 当前存储配置</h3>
      <div class="config-info">
        <p><strong>存储类型:</strong> 文件系统 (fs)</p>
        <p><strong>基础路径:</strong> ./data/db</p>
        <p><strong>目录:</strong> ./fSystem</p>
        <p><strong>存储名称:</strong> fSystem</p>
      </div>
    </div>
    
    <!-- 使用说明 -->
    <div class="usage-section">
      <h3>📚 使用方法</h3>
      <div class="code-example">
        <h4>在 API 路由中使用:</h4>
        <pre><code>// server/api/example.ts
export default defineEventHandler(async (event) => {
  // 写入数据
  await useStorage('fSystem').setItem('my-key', { data: 'value' })
  
  // 读取数据
  const data = await useStorage('fSystem').getItem('my-key')
  
  // 获取所有键
  const keys = await useStorage('fSystem').getKeys()
  
  return { data, keys }
})</code></pre>
      </div>
    </div>
  </div>
</template>

<style scoped>
.storage-test {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-section {
  margin: 20px 0;
}

.test-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
}

.test-button:hover:not(:disabled) {
  background-color: #0056b3;
}

.test-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.error-section {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  padding: 15px;
  margin: 20px 0;
}

.error-section h3 {
  color: #721c24;
  margin-top: 0;
}

.result-section {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 6px;
  padding: 15px;
  margin: 20px 0;
}

.result-section h3 {
  color: #155724;
  margin-top: 0;
}

.result-item {
  margin: 15px 0;
}

.result-item h4 {
  color: #155724;
  margin-bottom: 8px;
}

.config-section, .usage-section {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
  margin: 20px 0;
}

.config-info p {
  margin: 8px 0;
}

.code-example {
  margin-top: 15px;
}

pre {
  background-color: #f1f3f4;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

code {
  font-family: 'Courier New', monospace;
}
</style>
