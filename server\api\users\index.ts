// 定义 API 路由元数据 - 类似 Swagger 文档
defineRouteMeta({
  openAPI: {
    tags: ["用户管理"],
    summary: "获取用户列表",
    description: "返回系统中所有用户的分页列表，支持搜索和过滤功能",
    parameters: [
      {
        in: "query",
        name: "page",
        required: false,
        schema: { 
          type: "integer", 
          default: 1,
          minimum: 1
        },
        description: "页码，从1开始"
      },
      {
        in: "query",
        name: "limit",
        required: false,
        schema: { 
          type: "integer", 
          default: 10,
          minimum: 1,
          maximum: 100
        },
        description: "每页数量，最大100"
      },
      {
        in: "query",
        name: "search",
        required: false,
        schema: { type: "string" },
        description: "搜索关键词，支持用户名和邮箱搜索"
      }
    ],
    responses: {
      200: {
        description: "成功返回用户列表",
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                users: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      id: { type: "integer", description: "用户ID" },
                      name: { type: "string", description: "用户名" },
                      email: { type: "string", description: "邮箱地址" },
                      avatar: { type: "string", description: "头像URL" },
                      createdAt: { type: "string", format: "date-time", description: "创建时间" }
                    }
                  }
                },
                pagination: {
                  type: "object",
                  properties: {
                    page: { type: "integer", description: "当前页码" },
                    limit: { type: "integer", description: "每页数量" },
                    total: { type: "integer", description: "总记录数" },
                    totalPages: { type: "integer", description: "总页数" }
                  }
                }
              }
            }
          }
        }
      },
      400: {
        description: "请求参数错误",
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                error: { type: "string", description: "错误信息" }
              }
            }
          }
        }
      }
    }
  }
});

// 实际的 API 处理函数
export default defineEventHandler(async (event) => {
  const query = getQuery(event);
  
  // 参数验证
  const page = Math.max(1, Number(query.page) || 1);
  const limit = Math.min(100, Math.max(1, Number(query.limit) || 10));
  const search = query.search as string || '';
  
  // 模拟用户数据
  const allUsers = [
    { id: 1, name: "张三", email: "<EMAIL>", avatar: "/avatars/1.jpg", createdAt: "2024-01-01T00:00:00Z" },
    { id: 2, name: "李四", email: "<EMAIL>", avatar: "/avatars/2.jpg", createdAt: "2024-01-02T00:00:00Z" },
    { id: 3, name: "王五", email: "<EMAIL>", avatar: "/avatars/3.jpg", createdAt: "2024-01-03T00:00:00Z" },
    { id: 4, name: "赵六", email: "<EMAIL>", avatar: "/avatars/4.jpg", createdAt: "2024-01-04T00:00:00Z" },
    { id: 5, name: "钱七", email: "<EMAIL>", avatar: "/avatars/5.jpg", createdAt: "2024-01-05T00:00:00Z" },
  ];
  
  // 搜索过滤
  let filteredUsers = allUsers;
  if (search) {
    filteredUsers = allUsers.filter(user => 
      user.name.includes(search) || user.email.includes(search)
    );
  }
  
  // 分页
  const total = filteredUsers.length;
  const totalPages = Math.ceil(total / limit);
  const startIndex = (page - 1) * limit;
  const users = filteredUsers.slice(startIndex, startIndex + limit);
  
  return {
    users,
    pagination: {
      page,
      limit,
      total,
      totalPages
    }
  };
});
