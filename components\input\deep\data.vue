<script setup lang="ts">
const counting = ref(5)

onMounted(()=>{
  let timer = setInterval(()=>{
    console.log('%c [ counting.value ]-7', 'font-size:13px; background:#025444; color:#469888;', counting.value);
    counting.value--
    if(counting.value<=0){
      if(timer){
        clearInterval(timer)
        timer=null
      }
    }
  },1000)
})

</script>

<template>
  <div>
    <el-card>
      666
    </el-card>

    <el-loading v-if="counting" />
  </div>
</template>

<style lang="less" scoped>

</style>