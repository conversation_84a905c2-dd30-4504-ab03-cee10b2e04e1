export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  devtools: { enabled: true },
  modules: [
    '@nuxt/eslint',
    '@element-plus/nuxt'
  ],

  // Element Plus 配置
  elementPlus: {
    /** 配置选项 */
  },
  css: ['~/assets/css/main.css'],
  // 内置的postcss
  postcss: {
    plugins: {
      'postcss-nested': {},
      'postcss-custom-media': {}
    }
  },
  runtimeConfig: {
    // 服务器端环境变量（私有）
    apiTokenForText: process.env.API_TOKEN_FOR_TEXT || '',
    public: {
      // 客户端环境变量（公开）
      apiTokenForText: ''
    }
  },

  devServer: {
    port: Number(process.env.SERVER_PORT) || 8080,
  },

  app: {
    baseURL: process.env.PUBLIC_PATH || '/',
    head: {
      // title: 'Rex\'s Nuxt',
      htmlAttrs: {
        lang: 'en',
      },
      link: [
        { rel: 'icon', type: 'image/png', href: '/image.png' }
      ],
      charset: 'utf-8', // 修复字符集
      viewport: 'width=device-width, initial-scale=1, maximum-scale=1', // 修复视口

    },
    pageTransition: { name: 'page', mode: 'out-in' },
    buildAssetsDir: '/_static/',
    rootAttrs: {
      id: '_app',
      class: '_class'
    },
  },

  imports: {
    // autoImport: true,
    // scan: false,
    dirs: [
      'test'
    ]
  },

  components:[
    {
      path: '@/components',
    },
    {
      path: '@/components/global',
      global: true,

    },

    {
      path:'@/components/input',
      pathPrefix: false,
    }
  ],



  routeRules: {
    // // 为 SEO 目的在构建时生成
    // '/': { prerender: true },
    // // 缓存 1 小时
    // '/api/*': { cache: { maxAge: 60 * 60 } },
    // // 重定向以避免 404
    // '/old-page': {
    //     redirect: { to: '/api', statusCode: 302 }
    // }
    // ...
  },


  plugins: [
    { src: '~/plugins/far/far.ts', mode: 'server' },
    '~/plugins/baz/baz.ts'
  ],

  nitro: {
    runtimeConfig: {
    },
    // experimental: {
    //   openAPI:true,
    // }

    storage: {
      fSystem: {
        driver: 'fs',
        base: './data/db',
        dir: './fSystem'
      }
    },
    publicAssets: [
      {
        baseURL: "images777",
        dir: "public/99",
        maxAge: 60 * 60 * 24 * 7, // 7 天
      },
    ],
    serverAssets: [{
      baseName: 'my_directory',
      dir: './server/my_directory'
    },
    {
      baseName: 'templates',
      dir: './server/templates'
    }],
    devServer: {

    },
    watchOptions: {

    },
    virtual: {
    },

    // 路由相关
    // baseURL: '/baseUrl/',
    apiBaseURL: '/apiBaseUrl/',
    handlers: [
      {
        route: '/',
        middleware: true,
        handler: './server/log.ts'
      }

    ],
    //  devHandlers:[
    //    {
    //     route: '/',
    //     handler: (event) => {
    //     //  console.log('-----------',event)
    //     }
    //   }
    //  ]
    devProxy: {
      // '/test': {
      //   pathwrite: {
      //     '^/test': '/'
      //   },
      '/proxy/example': { target: 'https://example.com', changeOrigin: true }
    },
    errorHandler: './server/error-handler.ts',

    routeRules: {
      // '/api/**':{
      //   redirect:{
      //     to:'/fetch/**',
      //     statusCode:301
      //   },
      // },
      '/proxy/*': { proxy: 'https://localhost:8977/api/test' },
    },
    prerender: {

    }
  },





})