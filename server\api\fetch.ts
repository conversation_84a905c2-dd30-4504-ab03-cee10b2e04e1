export default defineEventHandler(async (event)=>{
  const prints = getRequestFingerprint(event,{ip:true})
  console.log('%c [ prints ]-3', 'font-size:13px; background:#5f38d3; color:#a37cff;', Object.keys(prints));


  if(isMethod(event,'GET')){
    return {
      num:1,
      prints
    };
  }

  if(isMethod(event,'POST')){
    return {
      num:2,
      prints
    };
  }
  // const query = await getValidatedQuery(event,(data)=>{
  // console.log('%c [ data ]-4', 'font-size:13px; background:#5bc306; color:#9fff4a;', data);
  //   return data.age >10
  // })

  // const query = getQuery(event)
  // const deleteKey = Object.keys(query)?.[0] as string
  // delete query[deleteKey]
  console.log('%c [ query ]-4', 'font-size:13px; background:#05eeec; color:#49ffff;', query);
  // return  new Error('报错了')

 
})