<script setup lang="ts">
definePageMeta({
	title: '某个页面',
})
useHead({
	title: 'about page',
})

const result = ref()
const age = ref()
const methods = ref()
const streamMessages = ref<string[]>([]) // 存储 SSE 消息
const isStreaming = ref(false) // 流状态
let eventSource: EventSource | null = null // SSE 连接

let refreshFn: () => unknown

const onFetch = async () => {
	if (refreshFn) {
		console.log(
			'%c [ refreshFn ]-16',
			'font-size:13px; background:#7c8753; color:#c0cb97;'
		)
		refreshFn?.()
		return
	}

	const { data, status } = await useFetch('/api/fetch', {
		// pick: ['age'],
		method: methods.value.toUpperCase(),
		query: {
			name: 'Rex2321123',
			sex: 'male',
			age: age, // 动态参数，需要响应式，需要使用 ref 包裹
		},
	})

	result.value = data.value
}

const onClear = () => {
	clearNuxtData()
	clearNuxtState()
}

const onStream = () => {
  if (isStreaming.value) {
    // 如果正在流式传输，则停止
    stopStream()
    return
  }

  // 清空之前的消息
  streamMessages.value = []
  isStreaming.value = true

  // 创建 EventSource 连接
  eventSource = new EventSource('/api/stream')

  // 监听消息事件
  eventSource.onmessage = (event) => {
    console.log('收到 SSE 消息:', event.data)
    streamMessages.value.push(event.data)
    result.value = streamMessages.value.join('\n')
  }

  // 监听连接打开事件
  eventSource.onopen = () => {
    console.log('SSE 连接已打开')
  }

  // 监听错误事件
  eventSource.onerror = (error) => {
    console.error('SSE 连接错误:', error)
    stopStream()
  }
}

// 停止 SSE 流
const stopStream = () => {
  if (eventSource) {
    eventSource.close()
    eventSource = null
  }
  isStreaming.value = false
}

// 组件卸载时清理 SSE 连接
onBeforeUnmount(() => {
  stopStream()
})

// onBeforeMount(async() => {
// 	await onFetch()
// })
</script>

<template>
	<section>
		<h2>API 测试页面</h2>

		<!-- 普通 API 结果 -->
		<div v-if="result">
			<h3>API 结果:</h3>
			<pre>{{ result }}</pre>
		</div>

		<!-- SSE 流状态和消息 -->
		<div class="sse-section">
			<h3>Server-Sent Events (SSE) 流:</h3>
			<div class="sse-status">
				状态: <span :class="{ 'streaming': isStreaming, 'stopped': !isStreaming }">
					{{ isStreaming ? '正在流式传输...' : '已停止' }}
				</span>
			</div>

			<div v-if="streamMessages.length > 0" class="sse-messages">
				<h4>接收到的消息:</h4>
				<div class="message-list">
					<div
						v-for="(message, index) in streamMessages"
						:key="index"
						class="message-item"
					>
						<span class="message-index">[{{ index + 1 }}]</span>
						<span class="message-content">{{ message }}</span>
					</div>
				</div>
			</div>
		</div>

		<hr>

		<!-- 表单控件 -->
		<div class="form-section">
			<el-input v-model="age" placeholder="输入年龄" />
			<p>age: {{ age }}</p>

			<el-input v-model="methods" placeholder="HTTP 方法" />
			<p>methods: {{ methods }}</p>
		</div>

		<!-- 操作按钮 -->
		<div class="button-section">
			<nuxt-link to="/"> 回到首页 </nuxt-link>
			<button @click="onFetch">普通 Fetch</button>
			<el-button
				:type="isStreaming ? 'warning' : 'danger'"
				@click="onStream"
			>
				{{ isStreaming ? '停止流' : '开始 SSE 流' }}
			</el-button>
			<button @click="onClear">清除数据</button>
		</div>
	</section>
</template>

<style scoped>
.sse-section {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.sse-status {
  margin-bottom: 15px;
  font-weight: bold;
}

.streaming {
  color: #67c23a;
}

.stopped {
  color: #909399;
}

.sse-messages {
  margin-top: 15px;
}

.message-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  padding: 10px;
}

.message-item {
  display: flex;
  margin-bottom: 8px;
  padding: 5px;
  border-bottom: 1px solid #f0f0f0;
}

.message-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.message-index {
  color: #909399;
  margin-right: 10px;
  min-width: 40px;
  font-family: monospace;
}

.message-content {
  color: #303133;
  flex: 1;
}

.form-section {
  margin: 20px 0;
}

.form-section .el-input {
  margin-bottom: 10px;
}

.button-section {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.button-section button,
.button-section .el-button {
  margin: 0;
}
</style>
